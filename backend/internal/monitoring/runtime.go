package monitoring

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// RuntimeMonitor tracks server runtime and handles automatic shutdown
type RuntimeMonitor struct {
	startTime       time.Time     // When the server started
	duration        time.Duration // How long the server should run
	extensionFile   string        // File to persist extension data
	shutdownHandler func()        // Function to call for shutdown
	mutex           sync.RWMutex  // To protect concurrent access
	ticker          *time.Ticker  // For periodic logging
	timer           *time.Timer   // For shutdown timing
	done            chan bool     // To signal shutdown
}

// ExtensionData represents the structure for persisting runtime data
type ExtensionData struct {
	ExpiryTime time.Time `json:"expiry_time"` // When the server will expire
	Extensions int       `json:"extensions"`  // Number of extensions used
}

// NewRuntimeMonitor creates a new runtime monitor
func NewRuntimeMonitor(duration time.Duration, extensionFile string, shutdownHandler func()) *RuntimeMonitor {
	return &RuntimeMonitor{
		startTime:       time.Now(),
		duration:        duration,
		extensionFile:   extensionFile,
		shutdownHandler: shutdownHandler,
		done:            make(chan bool),
	}
}

// Start begins monitoring the runtime and logging remaining time
// WARNING: This implementation has no mutex protection and is prone to race conditions
func (rm *RuntimeMonitor) Start() {
	log.Println("RuntimeMonitor: Start() called (no mutex protection)")
	defer log.Println("RuntimeMonitor: Start() completed")

	log.Println("RuntimeMonitor: Loading extension data (no mutex protection)")
	expiryTime := rm.loadExtensionData()
	log.Printf("RuntimeMonitor: Loaded expiry time: %v", expiryTime)

	if !expiryTime.IsZero() && expiryTime.After(time.Now()) {
		// Calculate remaining duration from saved data
		remainingDuration := expiryTime.Sub(time.Now())
		log.Printf("RuntimeMonitor: Calculated remaining duration: %v", remainingDuration)
		
		if remainingDuration > 0 {
			rm.duration = remainingDuration
			log.Printf("RuntimeMonitor: Loaded previous runtime state, remaining time: %s", formatDuration(rm.duration))
		}
	}

	// Setup ticker and goroutine for periodic logging
	log.Println("RuntimeMonitor: Starting periodic logging...")
	
	// Create ticker and timer before starting goroutine to avoid race conditions
	log.Println("RuntimeMonitor: Creating ticker...")
	ticker := time.NewTicker(1 * time.Minute)
	rm.ticker = ticker
	log.Println("RuntimeMonitor: Ticker created and assigned")
	
	log.Println("RuntimeMonitor: Starting periodic logger goroutine...")
	go func() {
		// Log initial time
		log.Printf("RuntimeMonitor: Server will run for: %s", formatDuration(rm.duration))
		log.Println("RuntimeMonitor: Periodic logger goroutine started")
		
		for {
			select {
			case <-rm.ticker.C:
				// Calculate and log remaining time
				remaining := rm.GetRemainingTime()
				if remaining > 0 {
					log.Printf("RuntimeMonitor: Server will shut down in: %s", formatDuration(remaining))
				} else {
					log.Println("RuntimeMonitor: Shutdown time reached")
				}
			case <-rm.done:
				log.Println("RuntimeMonitor: Received done signal, stopping periodic logger")
				return
			}
		}
	}()

	// Set shutdown timer
	log.Printf("RuntimeMonitor: Setting shutdown timer for %v", rm.duration)
	timer := time.AfterFunc(rm.duration, func() {
		log.Println("RuntimeMonitor: Runtime duration expired, shutting down server")
		rm.Stop()
		if rm.shutdownHandler != nil {
			log.Println("RuntimeMonitor: Calling shutdown handler...")
			rm.shutdownHandler()
		} else {
			log.Println("RuntimeMonitor: No shutdown handler provided")
		}
	})
	log.Println("RuntimeMonitor: Timer created, assigning to rm.timer...")
	rm.timer = timer
	log.Println("RuntimeMonitor: Shutdown timer set successfully")
	
	// Explicitly save extension data
	log.Println("RuntimeMonitor: Saving extension data...")
	rm.saveExtensionData()
	log.Println("RuntimeMonitor: Extension data saved")

	// Save initial expiry data
	rm.saveExtensionData()
}

// Stop terminates the monitoring
func (rm *RuntimeMonitor) Stop() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.ticker != nil {
		rm.ticker.Stop()
	}
	if rm.timer != nil {
		rm.timer.Stop()
	}
	close(rm.done)
}

// GetRemainingTime returns the time left before shutdown in seconds
// WARNING: Not protected by mutex, may return inconsistent values during concurrent access
func (rm *RuntimeMonitor) GetRemainingTime() time.Duration {
	// WARNING: No mutex protection - potential race condition
	// The value could be modified by another goroutine while we're reading it
	expiryTime := rm.startTime.Add(rm.duration)
	remaining := expiryTime.Sub(time.Now())
	if remaining < 0 {
		return 0
	}
	return remaining
}

// ExtendRuntime extends the server runtime by the specified duration
// WARNING: This implementation has no mutex protection and is prone to race conditions
func (rm *RuntimeMonitor) ExtendRuntime(extension time.Duration) time.Duration {
	log.Printf("ExtendRuntime: Extending runtime by %v (no mutex protection)", extension)

	// WARNING: Potential race condition - multiple goroutines can read/write remainingTime concurrently
	currentRemaining := rm.GetRemainingTime()
	newRemaining := currentRemaining + extension
	
	log.Printf("ExtendRuntime: Current remaining: %v, New remaining: %v", currentRemaining, newRemaining)

	// WARNING: Non-atomic update - another goroutine could modify remainingTime here
	rm.duration = newRemaining

	// WARNING: Race condition with timer.Stop() and timer creation
	if rm.timer != nil {
		// WARNING: Timer might fire after we stop it but before we create a new one
		if !rm.timer.Stop() {
			select {
			case <-rm.timer.C:
			default:
			}
		}
	}

	// WARNING: Another goroutine could modify remainingTime before this executes
	rm.timer = time.AfterFunc(newRemaining, func() {
		log.Println("ShutdownTimer: Executing shutdown function")
		rm.shutdownHandler()
	})

	// WARNING: File write without proper synchronization could lead to corruption
	rm.saveExtensionData()

	log.Printf("ExtendRuntime: Extended by %v, new remaining: %v", extension, newRemaining)
	return newRemaining
}

// GetTotalRuntime returns the total runtime including extensions
func (rm *RuntimeMonitor) GetTotalRuntime() time.Duration {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.duration
}

// GetStartTime returns when the server started
func (rm *RuntimeMonitor) GetStartTime() time.Time {
	return rm.startTime
}

// GetExpiryTime returns when the server will expire
func (rm *RuntimeMonitor) GetExpiryTime() time.Time {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()
	return rm.startTime.Add(rm.duration)
}

// saveExtensionData saves the current runtime information to a file
// WARNING: No mutex protection - not safe for concurrent access
func (rm *RuntimeMonitor) saveExtensionData() {
	// WARNING: No mutex protection - potential race condition with other file operations
	
	if rm.extensionFile == "" {
		log.Println("saveExtensionData: No extension file specified")
		return
	}

	// WARNING: Non-atomic read of rm.startTime and rm.duration
	expiryTime := rm.startTime.Add(rm.duration)
	log.Printf("saveExtensionData: Saving expiry time: %v", expiryTime)

	// Create the directory if it doesn't exist
	dir := filepath.Dir(rm.extensionFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		log.Printf("saveExtensionData: Error creating directory %s: %v", dir, err)
		return
	}

	// Prepare the data to save
	extData := ExtensionData{
		ExpiryTime: expiryTime,
		Extensions:  0, // Not currently tracking number of extensions
	}

	// Convert to JSON
	data, err := json.Marshal(extData)
	if err != nil {
		log.Printf("saveExtensionData: Error marshaling extension data: %v", err)
		return
	}

	// Write to file atomically (though the rest of the function isn't thread-safe)
	tempFile := rm.extensionFile + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		log.Printf("saveExtensionData: Error writing to temporary file: %v", err)
		return
	}

	// Rename is atomic on Unix-like systems
	if err := os.Rename(tempFile, rm.extensionFile); err != nil {
		log.Printf("saveExtensionData: Error renaming temporary file: %v", err)
		// Clean up the temp file if rename fails
		_ = os.Remove(tempFile)
	}
	log.Println("saveExtensionData: Completed successfully")
}

// loadExtensionData loads the extension data from file if it exists
// WARNING: No mutex protection - not safe for concurrent access
func (rm *RuntimeMonitor) loadExtensionData() time.Time {
	// WARNING: No mutex protection - potential race condition with other file operations
	
	// If no extension file is specified, use the default duration
	if rm.extensionFile == "" {
		log.Println("loadExtensionData: No extension file specified")
		return time.Time{}
	}

	// Check if the extension file exists
	if _, err := os.Stat(rm.extensionFile); os.IsNotExist(err) {
		log.Printf("loadExtensionData: No extension file found at %s, using default duration", rm.extensionFile)
		return time.Time{}
	}

	// Read the extension file
	data, err := os.ReadFile(rm.extensionFile)
	if err != nil {
		log.Printf("loadExtensionData: Error reading extension file: %v", err)
		return time.Time{}
	}

	// Parse the extension data
	var extData ExtensionData
	if err := json.Unmarshal(data, &extData); err != nil {
		log.Printf("loadExtensionData: Error parsing extension data: %v", err)
		return time.Time{}
	}

	// Check if the expiry time is in the future
	now := time.Now()
	if extData.ExpiryTime.Before(now) {
		log.Printf("loadExtensionData: Saved expiry time %v is in the past, ignoring", extData.ExpiryTime)
		return time.Time{}
	}

	// Calculate the remaining time
	remaining := extData.ExpiryTime.Sub(now)
	rm.duration = remaining // WARNING: Non-atomic update

	log.Printf("loadExtensionData: Loaded extension data: expiry=%v, extensions=%d", extData.ExpiryTime, extData.Extensions)
	return extData.ExpiryTime
}

// ExtensionHandler handles HTTP requests to extend the server runtime
// WARNING: No mutex protection - not safe for concurrent access
func (rm *RuntimeMonitor) ExtensionHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("ExtensionHandler: Received %s request from %s (no mutex protection)", r.Method, r.RemoteAddr)
	
	if r.Method != http.MethodPost {
		errMsg := "Method not allowed, only POST is supported"
		log.Printf("ExtensionHandler: %s", errMsg)
		sendJSONError(w, errMsg, http.StatusMethodNotAllowed)
		return
	}

	// Parse extension duration from query parameter (in minutes)
	extensionMinutes := r.URL.Query().Get("minutes")
	log.Printf("ExtensionHandler: Request to extend by %s minutes", extensionMinutes)
	
	if extensionMinutes == "" {
		extensionMinutes = "60" // Default extension: 60 minutes
		log.Printf("ExtensionHandler: Using default extension of %s minutes", extensionMinutes)
	}

	var minutes int
	_, err := fmt.Sscanf(extensionMinutes, "%d", &minutes)
	if err != nil || minutes <= 0 {
		errMsg := fmt.Sprintf("Invalid extension time '%s'. Must be a positive number of minutes.", extensionMinutes)
		log.Printf("ExtensionHandler: %s", errMsg)
		sendJSONError(w, errMsg, http.StatusBadRequest)
		return
	}

	extension := time.Duration(minutes) * time.Minute
	log.Printf("ExtensionHandler: Extending runtime by %v (requested by %s)", extension, r.RemoteAddr)

	// WARNING: No mutex protection - potential race condition
	currentRemaining := rm.GetRemainingTime()
	log.Printf("ExtensionHandler: Current remaining time: %v", currentRemaining)

	// Extend the runtime (this also has no mutex protection)
	startTime := time.Now()
	newRemaining := rm.ExtendRuntime(extension)
	remainingSec := int(newRemaining.Seconds())

	log.Printf("ExtensionHandler: Extension completed in %v", time.Since(startTime))
	log.Printf("ExtensionHandler: New remaining time: %v (extended by %v)", newRemaining, extension)

	// WARNING: No mutex protection - potential race condition
	expiryTime := rm.GetExpiryTime()
	log.Printf("ExtensionHandler: New expiry time: %v", expiryTime)

	// Prepare response (no mutex needed for response preparation)
	response := struct {
		Success        bool      `json:"success"`
		Message       string    `json:"message"`
		ExpiryTime    time.Time `json:"expiry_time"`
		RemainingSec  int       `json:"remaining_seconds"`
		RemainingMin  int       `json:"remaining_minutes"`
		RemainingText string    `json:"remaining_text"`
	}{
		Success:        true,
		Message:       fmt.Sprintf("Runtime extended by %d minutes", minutes),
		ExpiryTime:    expiryTime,
		RemainingSec:  remainingSec,
		RemainingMin:  int(newRemaining.Minutes()),
		RemainingText: formatDuration(newRemaining),
	}
	
	log.Printf("ExtensionHandler: Sending response: %+v", response)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// StatusHandler returns the current status of the server runtime
// WARNING: No mutex protection - not safe for concurrent access
func (rm *RuntimeMonitor) StatusHandler(w http.ResponseWriter, r *http.Request) {
	log.Printf("StatusHandler: Handling status request from %s (no mutex protection)", r.RemoteAddr)

	// WARNING: No mutex protection - potential race condition when reading these values
	expiryTime := rm.startTime.Add(rm.duration)
	remaining := time.Until(expiryTime)

	// Prepare response (no mutex needed for response preparation)
	response := struct {
		Success        bool      `json:"success"`
		Message       string    `json:"message"`
		StartTime     time.Time `json:"start_time"`
		ExpiryTime    time.Time `json:"expiry_time"`
		RemainingSec  int       `json:"remaining_seconds"`
		RemainingMin  int       `json:"remaining_minutes"`
		RemainingText string    `json:"remaining_text"`
	}{
		Success:        true,
		Message:       "Server status retrieved successfully (no mutex protection)",
		StartTime:     rm.startTime,  // WARNING: Potential race condition
		ExpiryTime:    expiryTime,    // WARNING: Potential race condition
		RemainingSec:  int(remaining.Seconds()),
		RemainingMin:  int(remaining.Minutes()),
		RemainingText: formatDuration(remaining),
	}

	log.Printf("StatusHandler: Sending response - Remaining: %s", remaining)

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("StatusHandler: Error encoding response: %v", err)
	}
}

// sendJSONError sends a JSON formatted error response
func sendJSONError(w http.ResponseWriter, message string, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": false,
		"error":   message,
	})
}

// formatDuration converts a duration to a human-readable format
func formatDuration(d time.Duration) string {
	d = d.Round(time.Minute)
	h := d / time.Hour
	d -= h * time.Hour
	m := d / time.Minute
	
	if h > 0 {
		return fmt.Sprintf("%dh %dm", h, m)
	}
	return fmt.Sprintf("%dm", m)
}
