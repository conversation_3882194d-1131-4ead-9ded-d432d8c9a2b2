[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:frontend工程stores目录下的模块export 自定义hook时，有一个bug需要解决 DESCRIPTION:当使用以下方式export 自定义hook时
export const useKeyboardActions = () => useKeyboardStore((state) => ({ // 关键在此处，导出一个对象})
会导致无限循环的页面重渲染，改为单个值的导出就没有问题
export const useKeyboardVisibility = () => useKeyboardStore((state) => state.isVisible); // 正常
对于此BUG，你有两种处理策略：1- 绕过问题，将该问题放入自己的记忆中，只用后一种写法，避免前一种写法。 2- 彻底解决该问题。


-[ ] NAME: 修正src/services/terminalTools.js文件 DESCRIPTION:terminalTools.js文件拷贝自旧工程，并未与当前工程完全整合。先重命名为：terminalTools.ts；它的作用是：封装xtermService.ts中的终端方法为agent智能体工具，工具定义采用OpenAI的工具定义模式。确保该模块文件无编译错误。

-[ ] NAME: 修正src/services/refactoredAIAgent.js文件 DESCRIPTION:refactoredAIAgent.js文件拷贝自旧工程，并未与当前工程完全整合。先重命名为：AIAgent.ts；它的作用是：实现了一个agent智能体模式，使用OpenAI SDK，支持工具调用。确保该模块文件无编译错误。

-[ ] NAME: 重构src/stores/aiStore.ts模块 DESCRIPTION: aiStore.ts 包含了 AIDialog UI 依赖的一些状态数据，但是缺乏对工具调用消息的支持，请参考src/stores/aiMessages.example.txt文件中handleStreamingChunk的代码，chunk是OpenAI SDK chat流式数据，包含工具调用信息。参考aiMessages.example.txt文件丰富 aiStore.ts 对工具调用的支持。后续AIDialog UI也需要支持工具调用消息的渲染。

-[ ] NAME:  DESCRIPTION:请参考src/stores/aiMessages.example.txt文件中handleStreamingChunk的代码，chunk是OpenAI SDK chat流式数据，包含工具调用信息。参考aiMessages.example.txt文件丰富 AIDialog UI 对工具调用消息的渲染。
