/**
 * Streaming Handler
 * 
 * Handles real-time streaming of messages and tool calls
 */

export class StreamingHandler {
  constructor() {
    this.messagesContainer = null;
    this.currentStreamingMessage = null;
    this.mixedStreamingMessage = [];
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements = new Map();
    
    // Module dependencies
    this.markdownRenderer = null;
    this.toolCallRenderer = null;
    this.messageFormatter = null;
  }

  /**
   * Set module dependencies
   */
  setMarkdownRenderer(markdownRenderer) {
    this.markdownRenderer = markdownRenderer;
  }

  setToolCallRenderer(toolCallRenderer) {
    this.toolCallRenderer = toolCallRenderer;
  }

  setMessageFormatter(messageFormatter) {
    this.messageFormatter = messageFormatter;
  }

  /**
   * Initialize the streaming handler
   * @param {HTMLElement} messagesContainer - Messages container element
   */
  initialize(messagesContainer) {
    this.messagesContainer = messagesContainer;
    console.log('Streaming Handler initialized');
  }

  /**
   * Start a new streaming message
   */
  startStreamingMessage() {
    // Create a new message element for streaming
    const messageDiv = document.createElement('div');
    messageDiv.className = 'ai-message ai-message-assistant streaming';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    contentDiv.innerHTML = '<span class="streaming-cursor">▋</span>';
    
    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);
    
    this.currentStreamingMessage = '';
    this.mixedStreamingMessage = [];
    this.streamingMessageElement = contentDiv;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
    
    this.scrollToBottom();
  }

  /**
   * Handle streaming chunk from AI agent
   * @param {object} chunk - Streaming chunk data
   */
  handleStreamingChunk(chunk) {
    if (!this.streamingMessageElement) return;
    const lastMessage = this.mixedStreamingMessage[this.mixedStreamingMessage.length - 1];
    
    if (chunk.type === 'content') {
      if(lastMessage?.type != 'content') {
        this.mixedStreamingMessage.push({
          type: 'content',
          content: ''
        });
      }
      this.handleContentChunk(chunk);
    } else if (chunk.type === 'tool_call') {
      if(lastMessage?.type != 'tool_call') {
        this.mixedStreamingMessage.push({
          type: 'tool_call',
          toolCall: {
            function: {
              name: '',
              arguments: ''
            },
            status: 'pending',
            id: ''
          }
        });
        this.currentStreamingMessage = '';
      }
      this.handleToolCallChunk(chunk);
    }
  }

  /**
   * Handle content chunk
   * @param {object} chunk - Content chunk
   */
  handleContentChunk(chunk) {
    this.currentStreamingMessage += chunk.content;
    const lastMessage = this.mixedStreamingMessage[this.mixedStreamingMessage.length - 1];
    lastMessage.content = this.currentStreamingMessage;
    
    this.rerender();
  }

  /**
   * Handle tool call chunk
   * @param {object} chunk - Tool call chunk
   */
  handleToolCallChunk(chunk) {
    const toolCall = chunk.toolCall;
    
    const lastMessage = this.mixedStreamingMessage[this.mixedStreamingMessage.length - 1];
    lastMessage.toolCall.function.name += toolCall.function?.name || '';
    lastMessage.toolCall.function.arguments += toolCall.function?.arguments || '';
    lastMessage.toolCall.status = toolCall.status || 'pending';
    lastMessage.toolCall.id = toolCall.id || lastMessage.toolCall.id;

    this.rerender();
  }

  /**
   * Handle tool call events
   * @param {object} event - Tool call event
   */
  handleToolCall(event) {
    if (event.type === 'start') {
      this.updateLastMessage({"status": "executing"});
      this.rerender();
    } else if (event.type === 'complete') {
      this.updateLastMessage({
        "status": "completed",
        "result": event.result
      });
      this.rerender();
    } else if (event.type === 'error') {
      this.updateLastMessage({
        "status": "error",
        "error": event.error
      });
      this.rerender();
    }
  }

  updateLastMessage(updateLastMessage) {
    if (!updateLastMessage || !this.mixedStreamingMessage.length) return;
    const lastMessage = this.mixedStreamingMessage[this.mixedStreamingMessage.length - 1];
    if (!lastMessage.toolCall || !lastMessage.toolCall.function) return;
    if (updateLastMessage.status !== undefined) lastMessage.toolCall.status = updateLastMessage.status;
    if (updateLastMessage.result !== undefined) lastMessage.toolCall.result = updateLastMessage.result;
    if (updateLastMessage.error !== undefined) lastMessage.toolCall.error = updateLastMessage.error;
  }

  rerender() {
    let mixedRenderedContent = '';
    for (const message of this.mixedStreamingMessage) {
      if (message.type === 'content') {
        mixedRenderedContent += this.markdownRenderer.renderMarkdown(message.content);
      } else if (message.type === 'tool_call') {
        mixedRenderedContent += this.toolCallRenderer.renderToolCall(message.toolCall);
      }
    }
    
    this.streamingMessageElement.innerHTML = mixedRenderedContent + '<span class="streaming-cursor">▋</span>';
    this.scrollToBottom();
  }

  /**
   * Reset streaming state
   */
  resetStreamingState() {
    //todo
  }

  /**
   * Get current streaming content
   * @returns {string} Current streaming message content
   */
  getCurrentStreamingContent() {
    return this.currentStreamingMessage;
  }

  /**
   * Scroll to bottom
   */
  scrollToBottom() {
    if (this.messagesContainer) {
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
  }
}

export default StreamingHandler;
