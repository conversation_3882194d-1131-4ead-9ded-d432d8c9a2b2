import { create } from 'zustand';
import type { AIDialogState, AIMessage } from '@/types';

interface AIStore extends AIDialogState {
  sessionId: string;
  isStreaming: boolean;
  currentStreamingMessage: string;
  
  // Actions
  setOpen: (open: boolean) => void;
  toggle: () => void;
  addMessage: (message: Omit<AIMessage, 'id'>) => void;
  updateMessage: (id: string, updates: Partial<AIMessage>) => void;
  removeMessage: (id: string) => void;
  clearMessages: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setStreaming: (streaming: boolean) => void;
  updateStreamingMessage: (content: string) => void;
  resetStreamingMessage: () => void;
  generateSessionId: () => string;
  resetSession: () => void;
}

// Generate unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const useAIStore = create<AIStore>((set) => ({
  // Initial state
  isOpen: false,
  messages: [],
  isLoading: false,
  error: null,
  sessionId: generateSessionId(),
  isStreaming: false,
  currentStreamingMessage: '',

  // Actions
  setOpen: (open: boolean) => set({ isOpen: open }),

  toggle: () => set((state) => ({ isOpen: !state.isOpen })),

  addMessage: (messageData: Omit<AIMessage, 'id'>) => {
    const message: AIMessage = {
      ...messageData,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: messageData.timestamp || new Date(),
    };
    
    set((state) => ({
      messages: [...state.messages, message],
    }));
  },

  updateMessage: (id: string, updates: Partial<AIMessage>) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  removeMessage: (id: string) => {
    set((state) => ({
      messages: state.messages.filter((msg) => msg.id !== id),
    }));
  },

  clearMessages: () => set({ messages: [] }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),

  setError: (error: string | null) => set({ error }),

  setStreaming: (streaming: boolean) => set({ isStreaming: streaming }),

  updateStreamingMessage: (content: string) => {
    set({ currentStreamingMessage: content });
  },

  resetStreamingMessage: () => set({ currentStreamingMessage: '' }),

  generateSessionId,

  resetSession: () => {
    const newSessionId = generateSessionId();
    set({
      sessionId: newSessionId,
      messages: [],
      isLoading: false,
      error: null,
      isStreaming: false,
      currentStreamingMessage: '',
    });
  },
}));

// Selector hooks for better performance
export const useAIDialogVisibility = () => useAIStore((state) => state.isOpen);
export const useAIMessages = () => useAIStore((state) => state.messages);
export const useAILoading = () => useAIStore((state) => state.isLoading);
export const useAIError = () => useAIStore((state) => state.error);
export const useIsStreaming = () => useAIStore((state) => state.isStreaming);
export const useCurrentStreamingMessage = () => useAIStore((state) => state.currentStreamingMessage);
export const useAISession = () => useAIStore((state) => state.sessionId);
export const useSetOpen = () => useAIStore((state) => state.setOpen);
export const useToggle = () => useAIStore((state) => state.toggle);
export const useAddMessage = () => useAIStore((state) => state.addMessage);
export const useUpdateMessage = () => useAIStore((state) => state.updateMessage);
export const useRemoveMessage = () => useAIStore((state) => state.removeMessage);
export const useClearMessages = () => useAIStore((state) => state.clearMessages);
export const useSetLoading = () => useAIStore((state) => state.setLoading);
export const useSetError = () => useAIStore((state) => state.setError);
export const useSetStreaming = () => useAIStore((state) => state.setStreaming);
export const useUpdateStreamingMessage = () => useAIStore((state) => state.updateStreamingMessage);
export const useResetStreamingMessage = () => useAIStore((state) => state.resetStreamingMessage);
export const useResetSession = () => useAIStore((state) => state.resetSession);
