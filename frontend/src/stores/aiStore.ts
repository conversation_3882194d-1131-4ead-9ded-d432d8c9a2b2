import { create } from 'zustand';
import type { AIDialogState, AIMessage, MixedStreamingMessage } from '@/types';

interface AIStore extends AIDialogState {
  sessionId: string;
  isStreaming: boolean;
  currentStreamingMessage: string;
  mixedStreamingMessage: MixedStreamingMessage[];

  // Actions
  setOpen: (open: boolean) => void;
  toggle: () => void;
  addMessage: (message: Omit<AIMessage, 'id'>) => void;
  updateMessage: (id: string, updates: Partial<AIMessage>) => void;
  removeMessage: (id: string) => void;
  clearMessages: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setStreaming: (streaming: boolean) => void;
  updateStreamingMessage: (content: string) => void;
  resetStreamingMessage: () => void;
  generateSessionId: () => string;
  resetSession: () => void;

  // New methods for tool calling support
  handleStreamingChunk: (chunk: StreamingChunk) => void;
  startStreamingMessage: () => void;
  finishStreamingMessage: () => void;
  handleToolCallEvent: (event: ToolCallEvent) => void;
  getMixedStreamingMessage: () => MixedStreamingMessage[];
}

// Streaming chunk interface for OpenAI SDK compatibility
interface StreamingChunk {
  type: 'content' | 'tool_call';
  content?: string;
  fullContent?: string;
  toolCall?: {
    id?: string;
    index?: number;
    type?: string;
    function?: {
      name?: string;
      arguments?: string;
    };
    status?: 'pending' | 'running' | 'complete' | 'error';
  };
  allToolCalls?: any[];
}

// Tool call event interface
interface ToolCallEvent {
  type: 'start' | 'complete' | 'error';
  toolCall: {
    id: string;
    function: {
      name: string;
      arguments: string;
    };
  };
  result?: any;
  error?: string;
  count: number;
}

// Generate unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

export const useAIStore = create<AIStore>((set, get) => ({
  // Initial state
  isOpen: false,
  messages: [],
  isLoading: false,
  error: null,
  sessionId: generateSessionId(),
  isStreaming: false,
  currentStreamingMessage: '',
  mixedStreamingMessage: [],

  // Actions
  setOpen: (open: boolean) => set({ isOpen: open }),

  toggle: () => set((state) => ({ isOpen: !state.isOpen })),

  addMessage: (messageData: Omit<AIMessage, 'id'>) => {
    const message: AIMessage = {
      ...messageData,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: messageData.timestamp || new Date(),
    };
    
    set((state) => ({
      messages: [...state.messages, message],
    }));
  },

  updateMessage: (id: string, updates: Partial<AIMessage>) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  removeMessage: (id: string) => {
    set((state) => ({
      messages: state.messages.filter((msg) => msg.id !== id),
    }));
  },

  clearMessages: () => set({ messages: [] }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),

  setError: (error: string | null) => set({ error }),

  setStreaming: (streaming: boolean) => set({ isStreaming: streaming }),

  updateStreamingMessage: (content: string) => {
    set({ currentStreamingMessage: content });
  },

  resetStreamingMessage: () => set({ currentStreamingMessage: '' }),

  generateSessionId,

  resetSession: () => {
    const newSessionId = generateSessionId();
    set({
      sessionId: newSessionId,
      messages: [],
      isLoading: false,
      error: null,
      isStreaming: false,
      currentStreamingMessage: '',
      mixedStreamingMessage: [],
    });
  },

  // New methods for tool calling support
  handleStreamingChunk: (chunk: StreamingChunk) => {
    const state = get();
    const lastMessage = state.mixedStreamingMessage[state.mixedStreamingMessage.length - 1];

    if (chunk.type === 'content') {
      if (lastMessage?.type !== 'content') {
        set((state) => ({
          mixedStreamingMessage: [...state.mixedStreamingMessage, {
            type: 'content',
            content: ''
          }]
        }));
      }

      // Update content
      const updatedContent = (lastMessage?.content || '') + (chunk.content || '');
      set((state) => {
        const newMixed = [...state.mixedStreamingMessage];
        if (newMixed.length > 0) {
          newMixed[newMixed.length - 1] = {
            ...newMixed[newMixed.length - 1],
            content: updatedContent
          };
        }
        return {
          mixedStreamingMessage: newMixed,
          currentStreamingMessage: updatedContent
        };
      });

    } else if (chunk.type === 'tool_call') {
      if (lastMessage?.type !== 'tool_call') {
        set((state) => ({
          mixedStreamingMessage: [...state.mixedStreamingMessage, {
            type: 'tool_call',
            toolCall: {
              id: chunk.toolCall?.id || '',
              function: {
                name: '',
                arguments: ''
              },
              status: 'pending'
            }
          }]
        }));
      }

      // Update tool call
      set((state) => {
        const newMixed = [...state.mixedStreamingMessage];
        if (newMixed.length > 0 && newMixed[newMixed.length - 1].type === 'tool_call') {
          const lastToolCall = newMixed[newMixed.length - 1].toolCall!;
          newMixed[newMixed.length - 1] = {
            ...newMixed[newMixed.length - 1],
            toolCall: {
              ...lastToolCall,
              id: chunk.toolCall?.id || lastToolCall.id,
              function: {
                name: lastToolCall.function.name + (chunk.toolCall?.function?.name || ''),
                arguments: lastToolCall.function.arguments + (chunk.toolCall?.function?.arguments || '')
              },
              status: chunk.toolCall?.status || lastToolCall.status
            }
          };
        }
        return { mixedStreamingMessage: newMixed };
      });
    }
  },

  startStreamingMessage: () => {
    set({
      isStreaming: true,
      currentStreamingMessage: '',
      mixedStreamingMessage: []
    });
  },

  finishStreamingMessage: () => {
    const state = get();

    // Create a new message with mixed content
    const message: AIMessage = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      role: 'assistant',
      content: state.currentStreamingMessage,
      timestamp: new Date(),
      isStreaming: false,
      mixedContent: [...state.mixedStreamingMessage]
    };

    set((state) => ({
      messages: [...state.messages, message],
      isStreaming: false,
      currentStreamingMessage: '',
      mixedStreamingMessage: []
    }));
  },

  handleToolCallEvent: (event: ToolCallEvent) => {
    const state = get();
    const lastMessage = state.mixedStreamingMessage[state.mixedStreamingMessage.length - 1];

    if (lastMessage?.type === 'tool_call' && lastMessage.toolCall) {
      set((state) => {
        const newMixed = [...state.mixedStreamingMessage];
        if (newMixed.length > 0 && newMixed[newMixed.length - 1].type === 'tool_call') {
          const lastToolCall = newMixed[newMixed.length - 1].toolCall!;
          newMixed[newMixed.length - 1] = {
            ...newMixed[newMixed.length - 1],
            toolCall: {
              ...lastToolCall,
              status: event.type === 'start' ? 'running' :
                     event.type === 'complete' ? 'complete' : 'error',
              result: event.result,
              error: event.error
            }
          };
        }
        return { mixedStreamingMessage: newMixed };
      });
    }
  },

  getMixedStreamingMessage: () => {
    return get().mixedStreamingMessage;
  },
}));

// Selector hooks for better performance
export const useAIDialogVisibility = () => useAIStore((state) => state.isOpen);
export const useAIMessages = () => useAIStore((state) => state.messages);
export const useAILoading = () => useAIStore((state) => state.isLoading);
export const useAIError = () => useAIStore((state) => state.error);
export const useIsStreaming = () => useAIStore((state) => state.isStreaming);
export const useCurrentStreamingMessage = () => useAIStore((state) => state.currentStreamingMessage);
export const useAISession = () => useAIStore((state) => state.sessionId);
export const useSetOpen = () => useAIStore((state) => state.setOpen);
export const useToggle = () => useAIStore((state) => state.toggle);
export const useAddMessage = () => useAIStore((state) => state.addMessage);
export const useUpdateMessage = () => useAIStore((state) => state.updateMessage);
export const useRemoveMessage = () => useAIStore((state) => state.removeMessage);
export const useClearMessages = () => useAIStore((state) => state.clearMessages);
export const useSetLoading = () => useAIStore((state) => state.setLoading);
export const useSetError = () => useAIStore((state) => state.setError);
export const useSetStreaming = () => useAIStore((state) => state.setStreaming);
export const useUpdateStreamingMessage = () => useAIStore((state) => state.updateStreamingMessage);
export const useResetStreamingMessage = () => useAIStore((state) => state.resetStreamingMessage);
export const useResetSession = () => useAIStore((state) => state.resetSession);

// New selector hooks for tool calling support
export const useMixedStreamingMessage = () => useAIStore((state) => state.mixedStreamingMessage);
export const useHandleStreamingChunk = () => useAIStore((state) => state.handleStreamingChunk);
export const useStartStreamingMessage = () => useAIStore((state) => state.startStreamingMessage);
export const useFinishStreamingMessage = () => useAIStore((state) => state.finishStreamingMessage);
export const useHandleToolCallEvent = () => useAIStore((state) => state.handleToolCallEvent);
export const useGetMixedStreamingMessage = () => useAIStore((state) => state.getMixedStreamingMessage);
