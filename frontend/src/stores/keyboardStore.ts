import { create } from 'zustand';
import type { KeyboardState, KeyboardLayout } from '@/types';
import { KEYBOARD_LAYOUTS, STORAGE_KEYS } from '@/constants';

interface KeyboardStore extends KeyboardState {
  layouts: Record<string, KeyboardLayout>;
  
  // Actions
  setVisible: (visible: boolean) => void;
  toggle: () => void;
  setLayout: (layout: string) => void;
  setShiftPressed: (pressed: boolean) => void;
  setCtrlPressed: (pressed: boolean) => void;
  setAltPressed: (pressed: boolean) => void;
  resetModifiers: () => void;
  saveLayout: () => void;
  loadLayout: () => void;
}

// Default keyboard layouts
const defaultLayouts: Record<string, KeyboardLayout> = {
  default: {
    name: 'QWERTY',
    rows: [
      [
        { key: '`' }, { key: '1' }, { key: '2' }, { key: '3' }, { key: '4' }, 
        { key: '5' }, { key: '6' }, { key: '7' }, { key: '8' }, { key: '9' }, 
        { key: '0' }, { key: '-' }, { key: '=' }, { key: 'Backspace', width: 2 }
      ],
      [
        { key: 'Tab', width: 1.5 }, { key: 'q' }, { key: 'w' }, { key: 'e' }, 
        { key: 'r' }, { key: 't' }, { key: 'y' }, { key: 'u' }, { key: 'i' }, 
        { key: 'o' }, { key: 'p' }, { key: '[' }, { key: ']' }, { key: '\\', width: 1.5 }
      ],
      [
        { key: 'CapsLock', width: 1.75 }, { key: 'a' }, { key: 's' }, { key: 'd' }, 
        { key: 'f' }, { key: 'g' }, { key: 'h' }, { key: 'j' }, { key: 'k' }, 
        { key: 'l' }, { key: ';' }, { key: "'" }, { key: 'Enter', width: 2.25 }
      ],
      [
        { key: 'Shift', width: 2.25 }, { key: 'z' }, { key: 'x' }, { key: 'c' }, 
        { key: 'v' }, { key: 'b' }, { key: 'n' }, { key: 'm' }, { key: ',' }, 
        { key: '.' }, { key: '/' }, { key: 'Shift', width: 2.75 }
      ],
      [
        { key: 'Ctrl', width: 1.25 }, { key: 'Alt', width: 1.25 }, 
        { key: 'Space', width: 6.25 }, { key: 'Alt', width: 1.25 }, 
        { key: 'Ctrl', width: 1.25 }, { key: 'ArrowLeft' }, { key: 'ArrowUp' }, 
        { key: 'ArrowDown' }, { key: 'ArrowRight' }
      ]
    ]
  },
  shift: {
    name: 'QWERTY Shift',
    rows: [
      [
        { key: '~' }, { key: '!' }, { key: '@' }, { key: '#' }, { key: '$' }, 
        { key: '%' }, { key: '^' }, { key: '&' }, { key: '*' }, { key: '(' }, 
        { key: ')' }, { key: '_' }, { key: '+' }, { key: 'Backspace', width: 2 }
      ],
      [
        { key: 'Tab', width: 1.5 }, { key: 'Q' }, { key: 'W' }, { key: 'E' }, 
        { key: 'R' }, { key: 'T' }, { key: 'Y' }, { key: 'U' }, { key: 'I' }, 
        { key: 'O' }, { key: 'P' }, { key: '{' }, { key: '}' }, { key: '|', width: 1.5 }
      ],
      [
        { key: 'CapsLock', width: 1.75 }, { key: 'A' }, { key: 'S' }, { key: 'D' }, 
        { key: 'F' }, { key: 'G' }, { key: 'H' }, { key: 'J' }, { key: 'K' }, 
        { key: 'L' }, { key: ':' }, { key: '"' }, { key: 'Enter', width: 2.25 }
      ],
      [
        { key: 'Shift', width: 2.25 }, { key: 'Z' }, { key: 'X' }, { key: 'C' }, 
        { key: 'V' }, { key: 'B' }, { key: 'N' }, { key: 'M' }, { key: '<' }, 
        { key: '>' }, { key: '?' }, { key: 'Shift', width: 2.75 }
      ],
      [
        { key: 'Ctrl', width: 1.25 }, { key: 'Alt', width: 1.25 }, 
        { key: 'Space', width: 6.25 }, { key: 'Alt', width: 1.25 }, 
        { key: 'Ctrl', width: 1.25 }, { key: 'ArrowLeft' }, { key: 'ArrowUp' }, 
        { key: 'ArrowDown' }, { key: 'ArrowRight' }
      ]
    ]
  },
  function: {
    name: 'Function Keys',
    rows: [
      [
        { key: 'Escape' }, { key: 'F1' }, { key: 'F2' }, { key: 'F3' }, { key: 'F4' }, 
        { key: 'F5' }, { key: 'F6' }, { key: 'F7' }, { key: 'F8' }, { key: 'F9' }, 
        { key: 'F10' }, { key: 'F11' }, { key: 'F12' }, { key: 'Delete' }
      ],
      [
        { key: 'Insert' }, { key: 'Home' }, { key: 'PageUp' }, { key: 'NumLock' }, 
        { key: '/' }, { key: '*' }, { key: '-' }, { key: '7' }, { key: '8' }, 
        { key: '9' }, { key: '+', width: 1 }
      ],
      [
        { key: 'Delete' }, { key: 'End' }, { key: 'PageDown' }, { key: '4' }, 
        { key: '5' }, { key: '6' }, { key: '1' }, { key: '2' }, { key: '3' }, 
        { key: 'Enter', width: 1 }
      ],
      [
        { key: 'ArrowUp' }, { key: '0', width: 2 }, { key: '.' }
      ],
      [
        { key: 'ArrowLeft' }, { key: 'ArrowDown' }, { key: 'ArrowRight' }
      ]
    ]
  }
};

// Load saved layout from localStorage
const loadSavedLayout = (): string => {
  try {
    const saved = localStorage.getItem(STORAGE_KEYS.KEYBOARD_LAYOUT);
    if (saved && defaultLayouts[saved]) {
      return saved;
    }
  } catch (error) {
    console.warn('Failed to load keyboard layout from localStorage:', error);
  }
  return KEYBOARD_LAYOUTS.QWERTY;
};

export const useKeyboardStore = create<KeyboardStore>((set, get) => ({
  // Initial state
  isVisible: false,
  currentLayout: loadSavedLayout(),
  shiftPressed: false,
  ctrlPressed: false,
  altPressed: false,
  layouts: defaultLayouts,

  // Actions
  setVisible: (visible: boolean) => set({ isVisible: visible }),

  toggle: () => set((state) => ({ isVisible: !state.isVisible })),

  setLayout: (layout: string) => {
    if (get().layouts[layout]) {
      set({ currentLayout: layout });
      get().saveLayout();
    }
  },

  setShiftPressed: (pressed: boolean) => set({ shiftPressed: pressed }),
  setCtrlPressed: (pressed: boolean) => set({ ctrlPressed: pressed }),
  setAltPressed: (pressed: boolean) => set({ altPressed: pressed }),

  resetModifiers: () => set({
    shiftPressed: false,
    ctrlPressed: false,
    altPressed: false,
  }),

  saveLayout: () => {
    try {
      const state = get();
      localStorage.setItem(STORAGE_KEYS.KEYBOARD_LAYOUT, state.currentLayout);
    } catch (error) {
      console.warn('Failed to save keyboard layout to localStorage:', error);
    }
  },

  loadLayout: () => {
    const layout = loadSavedLayout();
    set({ currentLayout: layout });
  },
}));

// Selector hooks for better performance
export const useKeyboardVisibility = () => useKeyboardStore((state) => state.isVisible);
export const useKeyboardLayout = () => useKeyboardStore((state) => state.currentLayout);
// export const useKeyboardModifiers = () => useKeyboardStore((state) => ({
//   shiftPressed: state.shiftPressed,
//   ctrlPressed: state.ctrlPressed,
//   altPressed: state.altPressed,
// }));
export const useShiftPressed = () => useKeyboardStore((state) => state.shiftPressed);
export const useCtrlPressed = () => useKeyboardStore((state) => state.ctrlPressed);
export const useAltPressed = () => useKeyboardStore((state) => state.altPressed);
export const useToggle = () => useKeyboardStore((state) => state.toggle);

export const useSetKeyboardLayout = () => useKeyboardStore((state) => state.setLayout);
export const useSetVisible = () => useKeyboardStore((state) => state.setVisible);
export const useSetShiftPressed = () => useKeyboardStore((state) => state.setShiftPressed);
export const useSetCtrlPressed = () => useKeyboardStore((state) => state.setCtrlPressed);
export const useSetAltPressed = () => useKeyboardStore((state) => state.setAltPressed);
export const useResetModifiers = () => useKeyboardStore((state) => state.resetModifiers);
export const useSaveLayout = () => useKeyboardStore((state) => state.saveLayout);
export const useLoadLayout = () => useKeyboardStore((state) => state.loadLayout);
export const useKeyboardLayouts = () => useKeyboardStore((state) => state.layouts);
// export const useKeyboardActions = () => useKeyboardStore((state) => ({
//   setVisible: state.setVisible,
//   toggle: state.toggle,
//   setLayout: state.setLayout,
//   setShiftPressed: state.setShiftPressed,
//   setCtrlPressed: state.setCtrlPressed,
//   setAltPressed: state.setAltPressed,
//   resetModifiers: state.resetModifiers,
// }));
