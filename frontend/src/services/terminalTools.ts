/**
 * Terminal Tools for AI Agent
 * 
 * This module provides tools that encapsulate xtermService functionality
 * for use by AI agents. Includes input and output tools for terminal interaction.
 */

import { xtermService } from './xtermService';

// Debug logging flag
let debugLogging = true;

/**
 * Enable or disable debug logging for terminal tools
 */
export function setDebugLogging(enabled: boolean): void {
  debugLogging = enabled;
  console.log(`[TerminalTools] Debug logging ${enabled ? 'enabled' : 'disabled'}`);
}

/**
 * Debug log function that respects the debug flag
 */
function debugLog(message: string, data: any = null): void {
  if (debugLogging) {
    if (data) {
      console.log(message, data);
    } else {
      console.log(message);
    }
  }
}

// OpenAI Tool Definition Types
interface ToolFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
    oneOf?: any[];
  };
}

interface Tool {
  function: ToolFunction;
  execute: (args: any) => Promise<any>;
}

interface CompositeCommand {
  mode: 'single_key' | 'key_combo' | 'command_line';
  single_key?: string;
  key_combo?: string;
  command_line?: string;
}

interface TerminalInputArgs {
  mode: 'single_key' | 'key_combo' | 'command_line' | 'composites';
  single_key?: string;
  key_combo?: string;
  command_line?: string;
  composites?: CompositeCommand[];
  execute?: boolean;
  delay?: number;
}

interface TerminalOutputArgs {
  mode: 'current_line' | 'multiple_lines' | 'full_screen' | 'line_range';
  line_count?: number;
  start_line?: number;
  end_line?: number;
}

interface WaitArgs {
  duration: number;
  reason?: string;
}

/**
 * Terminal Input Tool
 * Allows AI agent to send various types of input to the terminal
 */
export const terminalInputTool: Tool = {
  function: {
    name: 'terminal_input',
    description: 'Send input to the terminal. Supports single keys, key combinations, command lines, or the above three modes sequences.',
    parameters: {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          enum: ['single_key', 'key_combo', 'command_line', 'composites'],
          description: 'Input mode: single_key for individual keys, key_combo for key combinations, command_line for single commands, composites for a combination of the above three modes'
        },
        single_key: {
          type: 'string',
          description: 'Single key to send (required when mode is single_key). Examples: Enter, Tab, Escape, ArrowUp, etc.'
        },
        key_combo: {
          type: 'string',
          description: 'Key combination to send (required when mode is key_combo). Examples: Ctrl+C, Ctrl+Z, Alt+F4, etc.'
        },
        command_line: {
          type: 'string',
          description: 'Command line to send (required when mode is command_line). Examples: ls -la, pwd, cat file.txt'
        },
        composites: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              mode: {
                type: 'string',
                enum: ['single_key', 'key_combo', 'command_line']
              },
              single_key: {
                type: 'string',
                description: 'Single key to send (required when mode is single_key). Examples: Enter, Tab, Escape, ArrowUp, etc.'
              },
              key_combo: {
                type: 'string',
                description: 'Key combination to send (required when mode is key_combo). Examples: Ctrl+C, Ctrl+Z, Alt+F4, etc.'
              },
              command_line: {
                type: 'string',
                description: 'Command line to send (required when mode is command_line). Examples: ls -la, pwd, cat file.txt'
              },
            },
            required: ['mode']
          },
          description: 'Array of command objects to send sequentially (required when mode is composites)'
        },
        execute: {
          type: 'boolean',
          default: true,
          description: 'Whether to execute command immediately (press Enter). Only applies to command_line and composites modes'
        },
        delay: {
          type: 'number',
          default: 100,
          description: 'Delay in milliseconds between commands in composites mode'
        }
      },
      required: ['mode'],
      oneOf: [
        {
          properties: { mode: { const: 'single_key' } },
          required: ['single_key']
        },
        {
          properties: { mode: { const: 'key_combo' } },
          required: ['key_combo']
        },
        {
          properties: { mode: { const: 'command_line' } },
          required: ['command_line']
        },
        {
          properties: { mode: { const: 'composites' } },
          required: ['composites']
        }
      ]
    }
  },
  
  async execute(args: TerminalInputArgs) {
    try {
      // Check if xtermService is available
      if (!xtermService.isServiceReady()) {
        return {
          success: false,
          error: 'Terminal service is not ready',
          mode: args.mode
        };
      }

      const { mode, single_key, key_combo, command_line, composites, execute = true, delay = 100 } = args;

      switch (mode) {
        case 'single_key':
          if (!single_key) {
            return { message: 'single_key is required for single_key mode' };
          }
          await xtermService.sendKey(single_key);
          await new Promise(resolve => setTimeout(resolve, delay));
          return {
            message: `按键已键入: ${single_key}；请查看屏幕内容<terminalScreen>和光标位置<cursor>获取键入结果`
          };

        case 'key_combo':
          if (!key_combo) {
            return { message: 'key_combo is required for key_combo mode' };
          }
          await xtermService.sendKeyCombination(key_combo);
          await new Promise(resolve => setTimeout(resolve, delay));
          return {
            message: `组合键已键入: ${key_combo}；请查看屏幕内容<terminalScreen>和光标位置<cursor>获取键入结果`
          };

        case 'command_line':
          if (!command_line) {
            return { message: 'command_line is required for command_line mode' };
          }
          await xtermService.sendCommand(command_line, execute);
          // Handle delay for command execution
          if (execute && delay > 0) {
            await new Promise(resolve => setTimeout(resolve, delay));
          }
          return {
            message: `命令已键入: ${command_line}；请查看屏幕内容<terminalScreen>和光标位置<cursor>获取键入结果`
          };

        case 'composites':
          if (!composites || !Array.isArray(composites)) {
            return { message: 'composites must be an array for composites mode' };
          }
          
          // Execute each composite command sequentially
          for (const composite of composites) {
            const { mode: compMode, single_key: compSingleKey, key_combo: compKeyCombo, command_line: compCommandLine } = composite;
            
            switch (compMode) {
              case 'single_key':
                if (compSingleKey) {
                  await xtermService.sendKey(compSingleKey);
                }
                break;
              case 'key_combo':
                if (compKeyCombo) {
                  await xtermService.sendKeyCombination(compKeyCombo);
                }
                break;
              case 'command_line':
                if (compCommandLine) {
                  await xtermService.sendCommand(compCommandLine, execute);
                }
                break;
            }
            
            // Add delay between commands
            if (delay > 0) {
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
          
          return {
            message: `命令组已依次键入；请查看屏幕内容<terminalScreen>和光标位置<cursor>获取键入结果`
          };

        default:
          return {
            message: `Invalid mode: ${mode}. Must be one of: single_key, key_combo, command_line, composites`
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        mode: args.mode
      };
    }
  }
};

/**
 * Terminal Output Tool
 * Allows AI agent to retrieve terminal output content
 */
export const terminalOutputTool: Tool = {
  function: {
    name: 'terminal_output',
    description: 'Get output content from the terminal. Can retrieve single line, multiple lines, or full screen content.',
    parameters: {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          enum: ['current_line', 'multiple_lines', 'full_screen', 'line_range'],
          description: 'Output mode: current_line for cursor line, multiple_lines for recent lines, full_screen for all content, line_range for specific range'
        },
        line_count: {
          type: 'number',
          minimum: 1,
          maximum: 100,
          description: 'Number of recent lines to retrieve (required for multiple_lines mode)'
        },
        start_line: {
          type: 'number',
          minimum: 0,
          description: 'Starting line number for line_range mode (0-based)'
        },
        end_line: {
          type: 'number',
          minimum: 0,
          description: 'Ending line number for line_range mode (0-based, inclusive)'
        }
      },
      required: ['mode'],
      oneOf: [
        {
          properties: { mode: { const: 'current_line' } }
        },
        {
          properties: { mode: { const: 'multiple_lines' } },
          required: ['line_count']
        },
        {
          properties: { mode: { const: 'full_screen' } }
        },
        {
          properties: { mode: { const: 'line_range' } },
          required: ['start_line', 'end_line']
        }
      ]
    }
  },

  async execute(args: TerminalOutputArgs) {
    try {
      // Check if xtermService is available
      if (!xtermService.isServiceReady()) {
        return {
          success: false,
          error: 'Terminal service is not ready',
          mode: args.mode
        };
      }

      const { mode, line_count, start_line, end_line } = args;

      switch (mode) {
        case 'current_line':
          debugLog('[TerminalTools] Getting current line content...');

          // Note: getCurrentLineContent and getCursorPosition are not available in current xtermService
          // Using getAllContent as fallback
          const allContent = xtermService.getAllContent();
          const contentLines = allContent.split('\n');
          const currentLine = contentLines[contentLines.length - 2] || ''; // Last line is usually empty

          debugLog('[TerminalTools] Current line retrieved:', {
            content: `"${currentLine}"`,
            length: currentLine.length,
            totalLines: contentLines.length
          });

          return {
            success: true,
            mode: 'current_line',
            content: currentLine,
            message: `Retrieved current line content (${currentLine.length} characters)`
          };

        case 'multiple_lines':
          if (!line_count || line_count < 1) {
            return { success: false, error: 'line_count must be a positive number for multiple_lines mode' };
          }

          debugLog('[TerminalTools] Getting multiple lines:', { requested: line_count });

          // Note: getCursorPosition and getLineRangeContent are not available in current xtermService
          // Using getAllContent as fallback
          const multiAllContent = xtermService.getAllContent();
          const multiContentLines = multiAllContent.split('\n');
          const requestedLines = multiContentLines.slice(-line_count - 1, -1); // Exclude last empty line

          debugLog('[TerminalTools] Multiple lines retrieved:', {
            totalLines: multiContentLines.length,
            requestedCount: line_count,
            retrievedLines: requestedLines.length,
            content: requestedLines.slice(0, 3) // First 3 lines for debugging
          });

          return {
            success: true,
            mode: 'multiple_lines',
            content: requestedLines,
            content_string: requestedLines.join('\n'),
            line_count: requestedLines.length,
            message: `Retrieved ${requestedLines.length} recent lines`
          };

        case 'full_screen':
          debugLog('[TerminalTools] Getting full screen content...');

          // Check if xtermService methods are available
          if (!xtermService.getAllContent) {
            console.error('[TerminalTools] xtermService.getAllContent method not available');
            return {
              success: false,
              error: 'getAllContent method not available on xtermService'
            };
          }

          const fullContent = xtermService.getAllContent();
          const fullContentLines = fullContent.split('\n');

          debugLog('[TerminalTools] Full screen content retrieved:', {
            lineCount: fullContentLines.length,
            firstFewLines: fullContentLines.slice(0, 3),
            lastFewLines: fullContentLines.slice(-3),
            totalCharacters: fullContent.length
          });

          // Check for empty or suspicious content
          if (fullContentLines.length === 0) {
            debugLog('[TerminalTools] No lines retrieved from terminal');
          } else if (fullContentLines.every(line => line.trim() === '')) {
            debugLog('[TerminalTools] All lines are empty');
          }

          return {
            success: true,
            mode: 'full_screen',
            content: fullContentLines,
            content_string: fullContent,
            line_count: fullContentLines.length,
            message: `Retrieved full screen content (${fullContentLines.length} lines)`
          };

        case 'line_range':
          if (start_line === undefined || end_line === undefined) {
            return { success: false, error: 'start_line and end_line are required for line_range mode' };
          }

          if (start_line < 0 || end_line < start_line) {
            return { success: false, error: 'Invalid line range: start_line must be >= 0 and end_line must be >= start_line' };
          }

          // Note: getLineRangeContent is not available in current xtermService
          // Using getAllContent as fallback
          const rangeAllContent = xtermService.getAllContent();
          const rangeAllLines = rangeAllContent.split('\n');
          const rangeLines = rangeAllLines.slice(start_line, end_line + 1);

          return {
            success: true,
            mode: 'line_range',
            content: rangeLines,
            content_string: rangeLines.join('\n'),
            line_count: rangeLines.length,
            start_line: start_line,
            end_line: end_line,
            message: `Retrieved lines ${start_line} to ${end_line} (${rangeLines.length} lines)`
          };

        default:
          return {
            success: false,
            error: `Invalid mode: ${mode}. Must be one of: current_line, multiple_lines, full_screen, line_range`
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        mode: args.mode
      };
    }
  }
};

/**
 * Wait Tool
 * Allows AI agent to pause execution for a specified duration
 */
export const waitTool: Tool = {
  function: {
    name: 'wait',
    description: 'Wait for a specified duration before continuing. Useful when waiting for commands to complete or UI to load.',
    parameters: {
      type: 'object',
      properties: {
        duration: {
          type: 'number',
          minimum: 100,
          maximum: 30000,
          description: 'Duration to wait in milliseconds (100ms to 30s)'
        },
        reason: {
          type: 'string',
          description: 'Optional reason for waiting (for logging/debugging purposes)'
        }
      },
      required: ['duration']
    }
  },

  async execute(args: WaitArgs) {
    try {
      const { duration, reason } = args;

      if (duration < 100 || duration > 30000) {
        return {
          success: false,
          error: 'Duration must be between 100ms and 30000ms (30 seconds)',
          duration: duration
        };
      }

      const startTime = Date.now();

      // Wait for the specified duration
      await new Promise(resolve => setTimeout(resolve, duration));

      const actualDuration = Date.now() - startTime;

      return {
        success: true,
        duration: duration,
        actual_duration: actualDuration,
        reason: reason || 'No reason specified',
        message: `Waited for ${actualDuration}ms${reason ? ` (${reason})` : ''}`
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        duration: args.duration
      };
    }
  }
};

/**
 * Get all terminal tools
 */
export function getTerminalTools(): Tool[] {
  return [terminalInputTool, terminalOutputTool, waitTool];
}

/**
 * Register terminal tools with an AI agent
 */
export function registerTerminalTools(agent: any): any {
  agent.addTool(terminalInputTool);
  agent.addTool(terminalOutputTool);
  agent.addTool(waitTool);
  return agent;
}
