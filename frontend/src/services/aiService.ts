import type { AIMessage } from '@/types';
import { xtermService } from './xtermService';
import { AIAgent, aiAgentManager } from './AIAgent';
import { getTerminalTools } from './terminalTools';

export interface AIServiceConfig {
  apiUrl?: string;
  apiKey?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface StreamingChunk {
  type: 'content' | 'tool_call' | 'error' | 'complete';
  content?: string;
  toolCall?: {
    id: string;
    function: {
      name: string;
      arguments: string;
    };
    status?: 'pending' | 'running' | 'complete' | 'error';
    result?: any;
  };
  error?: string;
}

export interface AIContext {
  terminalContent?: string;
  sessionId: string;
  messages: AIMessage[];
}

class AIService {
  private config: AIServiceConfig;
  private abortController: AbortController | null = null;
  private currentAgent: AIAgent | null = null;

  constructor(config: AIServiceConfig = {}) {
    this.config = {
      apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
      apiKey: 'sk-omzuuipecajrmtpcohxzrngwsdcuadlovzkigtfzqltpgpdz',
      model: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      temperature: 0.7,
      maxTokens: 4000,
      ...config,
    };
  }

  /**
   * Get or create an AI agent for the session
   */
  private getAgent(sessionId: string): AIAgent {
    let agent = aiAgentManager.getAgent(sessionId);

    if (!agent) {
      // Create new agent with terminal tools
      agent = aiAgentManager.createAgent(sessionId, {
        apiKey: this.config.apiKey!,
        endpoint: this.config.apiUrl,
        model: this.config.model,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      });

      // Add terminal tools
      const terminalTools = getTerminalTools();
      terminalTools.forEach(tool => agent!.addTool(tool));

      // Initialize the agent
      agent.initialize().catch(error => {
        console.error('Failed to initialize AI agent:', error);
      });
    }

    return agent;
  }

  /**
   * Send a message to the AI service with streaming support using AIAgent
   */
  async sendMessage(
    message: string,
    context: AIContext,
    onChunk?: (chunk: StreamingChunk) => void,
    onComplete?: (response: any) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // Cancel any existing request
    this.cancelRequest();

    try {
      // Get or create agent for this session
      const agent = this.getAgent(context.sessionId);
      this.currentAgent = agent;

      // Set up event handlers for the agent
      const originalOnChunk = agent['onChunk'];
      const originalOnComplete = agent['onComplete'];
      const originalOnError = agent['onError'];

      // Temporarily override event handlers
      (agent as any).onChunk = (chunk: any) => {
        // Convert AIAgent chunk format to aiService StreamingChunk format
        if (chunk.type === 'content') {
          onChunk?.({
            type: 'content',
            content: chunk.content
          });
        } else if (chunk.type === 'tool_call') {
          onChunk?.({
            type: 'tool_call',
            toolCall: {
              id: chunk.toolCall?.id || '',
              function: {
                name: chunk.toolCall?.function?.name || '',
                arguments: chunk.toolCall?.function?.arguments || ''
              },
              status: 'pending'
            }
          });
        }
      };

      (agent as any).onComplete = (response: any) => {
        onComplete?.(response);
        // Restore original handlers
        (agent as any).onChunk = originalOnChunk;
        (agent as any).onComplete = originalOnComplete;
        (agent as any).onError = originalOnError;
      };

      (agent as any).onError = (error: Error) => {
        onError?.(error);
        // Restore original handlers
        (agent as any).onChunk = originalOnChunk;
        (agent as any).onComplete = originalOnComplete;
        (agent as any).onError = originalOnError;
      };

      // Process the message with the agent
      await agent.processMessage(message, context);

    } catch (error) {
      if (error instanceof Error) {
        console.error('AI service error:', error);
        onError?.(error);
      } else {
        const unknownError = new Error('Unknown error occurred');
        console.error('Unknown AI service error:', error);
        onError?.(unknownError);
      }
    } finally {
      this.currentAgent = null;
    }
  }

  /**
   * Cancel the current request
   */
  cancelRequest(): void {
    if (this.currentAgent) {
      this.currentAgent.terminate();
      this.currentAgent = null;
    }
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * Check if a request is currently in progress
   */
  isRequestInProgress(): boolean {
    return this.currentAgent?.getIsProcessing() || this.abortController !== null;
  }

  /**
   * Update service configuration
   */
  updateConfig(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  /**
   * Execute a terminal command via AI tool call
   */
  async executeTerminalCommand(command: string): Promise<boolean> {
    try {
      return await xtermService.sendCommand(command, true);
    } catch (error) {
      console.error('Failed to execute terminal command:', error);
      return false;
    }
  }

  /**
   * Send text to terminal via AI tool call
   */
  async sendTextToTerminal(text: string): Promise<boolean> {
    try {
      return await xtermService.sendRawData(text);
    } catch (error) {
      console.error('Failed to send text to terminal:', error);
      return false;
    }
  }

  /**
   * Get terminal content for AI context
   */
  getTerminalContext(): string {
    return xtermService.getAllContent();
  }

  /**
   * Get agent for a specific session
   */
  getAgentForSession(sessionId: string): AIAgent | undefined {
    return aiAgentManager.getAgent(sessionId);
  }

  /**
   * Clear agent session
   */
  clearAgentSession(sessionId: string): void {
    const agent = aiAgentManager.getAgent(sessionId);
    if (agent) {
      agent.clearSession();
    }
  }

  /**
   * Remove agent session
   */
  removeAgentSession(sessionId: string): boolean {
    const agent = aiAgentManager.getAgent(sessionId);
    if (agent) {
      agent.terminate();
    }
    return aiAgentManager.removeAgent(sessionId);
  }

  /**
   * Get agent statistics
   */
  getAgentStats(sessionId: string) {
    const agent = aiAgentManager.getAgent(sessionId);
    return agent?.getStats();
  }
}

// Export singleton instance
// Export singleton instance
export const aiService = new AIService();
export default aiService;
