import type { AIMessage } from '@/types';
import { xtermService } from './xtermService';

export interface AIServiceConfig {
  apiUrl?: string;
  apiKey?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface StreamingChunk {
  type: 'content' | 'tool_call' | 'error' | 'complete';
  content?: string;
  toolCall?: {
    id: string;
    function: {
      name: string;
      arguments: string;
    };
    status?: 'pending' | 'running' | 'complete' | 'error';
    result?: any;
  };
  error?: string;
}

export interface AIContext {
  terminalContent?: string;
  sessionId: string;
  messages: AIMessage[];
}

class AIService {
  private config: AIServiceConfig;
  private abortController: AbortController | null = null;

  constructor(config: AIServiceConfig = {}) {
    this.config = {
      apiUrl: 'https://api.siliconflow.cn/v1/chat/completions',
      apiKey: 'sk-omzuuipecajrmtpcohxzrngwsdcuadlovzkigtfzqltpgpdz',
      model: 'Qwen/Qwen3-235B-A22B-Instruct-2507',
      temperature: 0.7,
      maxTokens: 4000,
      ...config,
    };
  }

  /**
   * Send a message to the AI service with streaming support
   */
  async sendMessage(
    message: string,
    context: AIContext,
    onChunk?: (chunk: StreamingChunk) => void,
    onComplete?: (response: any) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    // Cancel any existing request
    this.cancelRequest();

    // Create new abort controller
    this.abortController = new AbortController();

    try {
      // Get terminal context
      // const terminalContent = xtermService.getAllContent();

      // Prepare request payload
      const payload = {
        model: this.config.model,
        messages: context.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        stream: true
      };

      // Make streaming request
      const response = await fetch(this.config.apiUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle streaming response
      await this.handleStreamingResponse(response, onChunk, onComplete, onError);

    } catch (error) {
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          console.log('AI request was cancelled');
          return;
        }
        console.error('AI service error:', error);
        onError?.(error);
      } else {
        const unknownError = new Error('Unknown error occurred');
        console.error('Unknown AI service error:', error);
        onError?.(unknownError);
      }
    } finally {
      this.abortController = null;
    }
  }

  /**
   * Handle streaming response from the AI service
   */
  private async handleStreamingResponse(
    response: Response,
    onChunk?: (chunk: StreamingChunk) => void,
    onComplete?: (response: any) => void,
    onError?: (error: Error) => void
  ): Promise<void> {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is not readable');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        // Decode chunk and add to buffer
        buffer += decoder.decode(value, { stream: true });

        // Process complete lines
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim() === '') continue;

          try {
            // Parse SSE format
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              
              if (data === '[DONE]') {
                onComplete?.({ status: 'complete' });
                return;
              }

              const chunk = JSON.parse(data) as StreamingChunk;
              onChunk?.(chunk);
            }
          } catch (parseError) {
            console.warn('Failed to parse streaming chunk:', parseError, line);
          }
        }
      }

      // Handle any remaining buffer content
      if (buffer.trim()) {
        try {
          const chunk = JSON.parse(buffer) as StreamingChunk;
          onChunk?.(chunk);
        } catch (parseError) {
          console.warn('Failed to parse final chunk:', parseError);
        }
      }

      onComplete?.({ status: 'complete' });

    } catch (error) {
      if (error instanceof Error) {
        onError?.(error);
      } else {
        onError?.(new Error('Unknown streaming error'));
      }
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * Cancel the current request
   */
  cancelRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * Check if a request is currently in progress
   */
  isRequestInProgress(): boolean {
    return this.abortController !== null;
  }

  /**
   * Update service configuration
   */
  updateConfig(config: Partial<AIServiceConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  getConfig(): AIServiceConfig {
    return { ...this.config };
  }

  /**
   * Execute a terminal command via AI tool call
   */
  async executeTerminalCommand(command: string): Promise<boolean> {
    try {
      return await xtermService.sendCommand(command, true);
    } catch (error) {
      console.error('Failed to execute terminal command:', error);
      return false;
    }
  }

  /**
   * Send text to terminal via AI tool call
   */
  async sendTextToTerminal(text: string): Promise<boolean> {
    try {
      return await xtermService.sendRawData(text);
    } catch (error) {
      console.error('Failed to send text to terminal:', error);
      return false;
    }
  }

  /**
   * Get terminal content for AI context
   */
  getTerminalContext(): string {
    return xtermService.getAllContent();
  }
}

// Export singleton instance
// Export singleton instance
export const aiService = new AIService();
export default aiService;
