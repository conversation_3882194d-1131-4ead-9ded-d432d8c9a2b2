import { useEffect, useState } from 'react';
import { ConfigProvider, theme } from 'antd';

import Layout from '@/components/Layout/Layout';
import Terminal from '@/components/Terminal/Terminal';
import VirtualKeyboard from '@/components/VirtualKeyboard/VirtualKeyboard';
import AIDialog from '@/components/AIDialog/AIDialog';
import ContentModal from '@/components/ContentModal/ContentModal';
import SettingsModal from '@/components/SettingsModal/SettingsModal';
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary';
import { useWebSocketStore } from '@/stores/websocketStore';
import { xtermService } from '@/services/xtermService';
import { useToggle as useToggleKeyboard } from './stores/keyboardStore';
import { useToggle as useToggleAI } from './stores/aiStore';

function App() {
  const { connect, disconnect } = useWebSocketStore();  
  const toggleKeyboard = useToggleKeyboard();
  const toggleAI = useToggleAI();

  // State for modals
  const [contentModalOpen, setContentModalOpen] = useState(false);
  const [terminalContent, setTerminalContent] = useState('');
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);

  useEffect(() => {
    console.log('App mounting, connecting to WebSocket...');
    // Connect to WebSocket when app starts
    connect();

    // Cleanup on unmount
    return () => {
      console.log('App unmounting, disconnecting...');
      disconnect();
    };
  }, [connect, disconnect]);

  const handleTerminalReady = () => {
    console.log('Terminal is ready');
  };

  // Placeholder handlers for Layout component
  const handleGetAllContent = () => {
    let content = xtermService.getAllContent();
    content = "debugger...."
    console.log('Terminal content:', content);
    if (content) {
      setTerminalContent(content);
      setContentModalOpen(true);
    } else {
      console.warn('No terminal content available');
    }
  };

  const handleToggleKeyboard = () => {
    console.log('Toggle keyboard');
    toggleKeyboard();
  };

  const handleOpenAI = () => {
    console.log('Open AI dialog');
    toggleAI();
  };

  const handleOpenSettings = () => {
    console.log('Open settings');
    setSettingsModalOpen(true);
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        theme={{
          algorithm: theme.darkAlgorithm,
          token: {
            colorPrimary: '#52c41a',
            colorBgBase: '#1e1e1e',
            colorTextBase: '#f0f0f0',
          },
        }}
      >
        <Layout
          onGetAllContent={handleGetAllContent}
          onToggleKeyboard={handleToggleKeyboard}
          onOpenAI={handleOpenAI}
          onOpenSettings={handleOpenSettings}
        >
          <Terminal onReady={handleTerminalReady} />
          <VirtualKeyboard />
          <ContentModal
            open={contentModalOpen}
            content={terminalContent}
            onClose={() => setContentModalOpen(false)}
          />
          <AIDialog />
          <SettingsModal open={settingsModalOpen} onClose={() => setSettingsModalOpen(false)} />
        </Layout>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
