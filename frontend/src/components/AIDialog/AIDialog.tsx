import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Modal, Input, Button, Space, Typography, Divider, message } from 'antd';
import { SendOutlined, StopOutlined, ClearOutlined, RobotOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import MessageList from './MessageList';
import {
  useAIStore,
  useAIDialogVisibility,
  useAIMessages,
  useAILoading,
  useAIError,
  useSetOpen,
  useAddMessage,
  useUpdateMessage,
  useClearMessages,
  useSetLoading,
  useSetError,
  useSetStreaming,
  useUpdateStreamingMessage,
  useResetStreamingMessage,
  useResetSession,
  useIsStreaming,
  useCurrentStreamingMessage,
} from '@/stores/aiStore';
import { aiService, type StreamingChunk } from '@/services/aiService';
import type { AIDialogProps } from '@/types';
import { AI_CONFIG } from '@/constants';

const { TextArea } = Input;
const { Text } = Typography;

const StyledModal = styled(Modal)`
  .ant-modal-content {
    background: #1e1e1e;
    height: 80vh;
    display: flex;
    flex-direction: column;
  }
  
  .ant-modal-header {
    background: #1e1e1e;
    border-bottom: 1px solid #444;
    flex-shrink: 0;
    
    .ant-modal-title {
      color: #f0f0f0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .ant-modal-body {
    background: #1e1e1e;
    padding: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .ant-modal-footer {
    background: #1e1e1e;
    border-top: 1px solid #444;
    flex-shrink: 0;
    
    .ant-btn {
      background: #333;
      border-color: #555;
      color: #f0f0f0;
      
      &:hover {
        background: #444;
        border-color: #666;
        color: #fff;
      }
      
      &.ant-btn-primary {
        background: #52c41a;
        border-color: #52c41a;
        
        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
      
      &.ant-btn-dangerous {
        background: #ff4d4f;
        border-color: #ff4d4f;
        
        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }
`;

const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
`;

const InputContainer = styled.div`
  padding: 16px;
  border-top: 1px solid #444;
  background: #2d2d2d;
  flex-shrink: 0;
`;

const StyledTextArea = styled(TextArea)`
  background: #1e1e1e !important;
  border-color: #555 !important;
  color: #f0f0f0 !important;
  resize: none;
  
  &:focus {
    border-color: #52c41a !important;
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
  }
  
  &::placeholder {
    color: #888 !important;
  }
`;

const StatusBar = styled.div`
  padding: 8px 16px;
  background: #2d2d2d;
  border-top: 1px solid #444;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
`;

const AIDialog: React.FC<AIDialogProps> = ({ open: propOpen, onClose: propOnClose }) => {
  const isOpen = useAIDialogVisibility();
  const messages = useAIMessages();
  const isLoading = useAILoading();
  const error = useAIError();
  const isStreaming = useIsStreaming();
  const currentStreamingMessage = useCurrentStreamingMessage();
  // const {
  //   setOpen,
  //   addMessage,
  //   updateMessage,
  //   clearMessages,
  //   setLoading,
  //   setError,
  //   setStreaming,
  //   updateStreamingMessage,
  //   resetStreamingMessage,
  //   resetSession,
  // } = useAIActions();
  const setOpen = useSetOpen();
  const addMessage = useAddMessage();
  const updateMessage = useUpdateMessage();
  const clearMessages = useClearMessages();
  const setLoading = useSetLoading();
  const setError = useSetError();
  const setStreaming = useSetStreaming();
  const updateStreamingMessage = useUpdateStreamingMessage();
  const resetStreamingMessage = useResetStreamingMessage();
  const resetSession = useResetSession();

  const [inputValue, setInputValue] = useState('');
  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);
  const inputRef = useRef<any>(null);

  // Use prop open state if provided, otherwise use store state
  const visible = propOpen !== undefined ? propOpen : isOpen;

  // Handle modal close
  const handleClose = useCallback(() => {
    if (propOnClose) {
      propOnClose();
    } else {
      setOpen(false);
    }
  }, [propOnClose, setOpen]);

  // Handle input change
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    if (value.length <= AI_CONFIG.MAX_MESSAGE_LENGTH) {
      setInputValue(value);
    }
  }, []);

  // Handle key press in input
  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (inputValue.trim()) {
        handleSendMessage();
      }
    }
  }, [inputValue]);

  // Handle streaming chunk
  const handleStreamingChunk = useCallback((chunk: StreamingChunk) => {
    if (chunk.type === 'content' && chunk.content) {
      updateStreamingMessage(currentStreamingMessage + chunk.content);
      
      // Update the streaming message in the store
      if (currentStreamingId) {
        updateMessage(currentStreamingId, {
          content: currentStreamingMessage + chunk.content,
          isStreaming: true,
        });
      }
    } else if (chunk.type === 'tool_call') {
      // Handle tool calls
      console.log('Tool call:', chunk.toolCall);
    } else if (chunk.type === 'error') {
      setError(chunk.error || 'Unknown error occurred');
      setLoading(false);
      setStreaming(false);
      resetStreamingMessage();
      setCurrentStreamingId(null);
    }
  }, [currentStreamingMessage, currentStreamingId, updateStreamingMessage, updateMessage, setError, setLoading, setStreaming, resetStreamingMessage]);

  // Handle streaming complete
  const handleStreamingComplete = useCallback(() => {
    if (currentStreamingId) {
      updateMessage(currentStreamingId, {
        isStreaming: false,
      });
    }
    
    setLoading(false);
    setStreaming(false);
    resetStreamingMessage();
    setCurrentStreamingId(null);
  }, [currentStreamingId, updateMessage, setLoading, setStreaming, resetStreamingMessage]);

  // Handle streaming error
  const handleStreamingError = useCallback((error: Error) => {
    setError(error.message);
    setLoading(false);
    setStreaming(false);
    resetStreamingMessage();
    setCurrentStreamingId(null);
  }, [setError, setLoading, setStreaming, resetStreamingMessage]);

  // Send message
  const handleSendMessage = useCallback(async () => {
    const message = inputValue.trim();
    if (!message || isLoading) return;

    // 保存当前输入值用于错误恢复
    const currentInput = inputValue;
    
    // Clear input
    setInputValue('');
    setError(null);

    // Add user message
    addMessage({
      role: 'user',
      content: message,
      timestamp: new Date(),
    });

    // Create streaming assistant message
    const streamingId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setCurrentStreamingId(streamingId);

    // Set loading state
    setLoading(true);
    setStreaming(true);
    resetStreamingMessage();

    try {
      // Send message to AI service
      await aiService.sendMessage(
        message,
        {
          sessionId: useAIStore.getState().sessionId,
          messages: useAIStore.getState().messages,
        },
        handleStreamingChunk,
        handleStreamingComplete,
        handleStreamingError
      );
    } catch (error) {
      console.error('Failed to send message:', error);
      handleStreamingError(error instanceof Error ? error : new Error('Unknown error'));
      // 恢复输入框内容
      setInputValue(currentInput);
    }
  }, [
    inputValue,
    isLoading,
    addMessage,
    setLoading,
    setStreaming,
    setError,
    resetStreamingMessage,
    handleStreamingChunk,
    handleStreamingComplete,
    handleStreamingError
  ]);

  // Stop current request
  const handleStopRequest = useCallback(() => {
    aiService.cancelRequest();
    handleStreamingComplete();
    message.info('Request cancelled');
  }, [handleStreamingComplete]);

  // Clear messages
  const handleClearMessages = useCallback(() => {
    clearMessages();
    resetSession();
    message.success('Chat cleared');
  }, [clearMessages, resetSession]);

  // Focus input when modal opens
  useEffect(() => {
    if (visible && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [visible]);

  return (
    <StyledModal
      title={
        <>
          <RobotOutlined />
          AI Assistant
        </>
      }
      open={visible}
      onCancel={handleClose}
      width={800}
      footer={null}
      destroyOnClose={false}
    >
      <ChatContainer>
        <MessageList messages={messages} />
        
        <StatusBar>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {messages.length} messages • {isStreaming ? 'AI is typing...' : 'Ready'}
          </Text>
          <Space size="small">
            <Button
              size="small"
              icon={<ClearOutlined />}
              onClick={handleClearMessages}
              disabled={isLoading}
            >
              Clear
            </Button>
          </Space>
        </StatusBar>

        <InputContainer>
          <Space.Compact style={{ width: '100%' }}>
            <StyledTextArea
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading}
            />
            <Button
              type="primary"
              icon={isLoading ? <StopOutlined /> : <SendOutlined />}
              onClick={isLoading ? handleStopRequest : handleSendMessage}
              disabled={!inputValue.trim() && !isLoading}
              danger={isLoading}
            >
              {isLoading ? 'Stop' : 'Send'}
            </Button>
          </Space.Compact>
          
          {error && (
            <>
              <Divider style={{ margin: '8px 0' }} />
              <Text type="danger" style={{ fontSize: '12px' }}>
                Error: {error}
              </Text>
            </>
          )}
        </InputContainer>
      </ChatContainer>
    </StyledModal>
  );
};

export default AIDialog;
