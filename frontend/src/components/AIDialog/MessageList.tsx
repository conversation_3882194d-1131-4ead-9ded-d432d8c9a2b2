import React, { useEffect, useRef } from 'react';
import { Typography, Avatar } from 'antd';
import { UserOutlined, RobotOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { marked } from 'marked';

import type { AIMessage } from '@/types';
import MixedContentRenderer from './MixedContentRenderer';

const { Text } = Typography;

const MessagesContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #1e1e1e;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #2d2d2d;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;
    
    &:hover {
      background: #777;
    }
  }
`;

const MessageItem = styled.div<{ $isUser: boolean }>`
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  align-items: flex-start;
  flex-direction: ${props => props.$isUser ? 'row-reverse' : 'row'};
`;

const MessageBubble = styled.div<{ $isUser: boolean; $isStreaming?: boolean }>`
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  background: ${props => props.$isUser ? '#52c41a' : '#2d2d2d'};
  color: ${props => props.$isUser ? '#fff' : '#f0f0f0'};
  border: 1px solid ${props => props.$isUser ? '#52c41a' : '#444'};
  position: relative;
  
  ${props => props.$isStreaming && `
    &::after {
      content: '▋';
      animation: blink 1s infinite;
      margin-left: 4px;
    }
    
    @keyframes blink {
      0%, 50% { opacity: 1; }
      51%, 100% { opacity: 0; }
    }
  `}
`;

const MessageContent = styled.div<{ $isUser: boolean }>`
  word-wrap: break-word;
  line-height: 1.5;
  
  ${props => !props.$isUser && `
    h1, h2, h3, h4, h5, h6 {
      color: #f0f0f0;
      margin: 8px 0 4px 0;
    }
    
    p {
      margin: 4px 0;
    }
    
    code {
      background: #1e1e1e;
      color: #52c41a;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    }
    
    pre {
      background: #1e1e1e;
      border: 1px solid #444;
      border-radius: 6px;
      padding: 12px;
      margin: 8px 0;
      overflow-x: auto;
      
      code {
        background: none;
        padding: 0;
      }
    }
    
    blockquote {
      border-left: 3px solid #52c41a;
      padding-left: 12px;
      margin: 8px 0;
      color: #ccc;
    }
    
    ul, ol {
      margin: 8px 0;
      padding-left: 20px;
    }
    
    li {
      margin: 2px 0;
    }
    
    a {
      color: #52c41a;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
    
    table {
      border-collapse: collapse;
      margin: 8px 0;
      width: 100%;
    }
    
    th, td {
      border: 1px solid #444;
      padding: 6px 8px;
      text-align: left;
    }
    
    th {
      background: #333;
      font-weight: bold;
    }
  `}
`;

const MessageMeta = styled.div<{ $isUser: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
  justify-content: ${props => props.$isUser ? 'flex-end' : 'flex-start'};
`;

const MessageTime = styled(Text)`
  font-size: 11px;
  color: #888 !important;
`;

const MessageAvatar = styled(Avatar)<{ $isUser: boolean }>`
  background: ${props => props.$isUser ? '#52c41a' : '#1890ff'};
  flex-shrink: 0;
`;

interface MessageListProps {
  messages: AIMessage[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Configure marked for better rendering
  useEffect(() => {
    marked.setOptions({
      breaks: true,
      gfm: true,
    });
  }, []);

  const formatTime = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const renderMessageContent = (message: AIMessage): string => {
    if (message.role === 'user') {
      // For user messages, just return plain text with line breaks
      return message.content.replace(/\n/g, '<br>');
    } else {
      // For assistant messages, render markdown
      try {
        const result = marked(message.content);
        return typeof result === 'string' ? result : message.content.replace(/\n/g, '<br>');
      } catch (error) {
        console.error('Failed to render markdown:', error);
        return message.content.replace(/\n/g, '<br>');
      }
    }
  };

  const hasToolCalls = (message: AIMessage): boolean => {
    return !!(message.tool_calls?.length || message.mixedContent?.length);
  };

  if (messages.length === 0) {
    return (
      <MessagesContainer>
        <div style={{ 
          textAlign: 'center', 
          color: '#888', 
          marginTop: '50px',
          fontSize: '14px' 
        }}>
          <RobotOutlined style={{ fontSize: '32px', marginBottom: '16px' }} />
          <div>Start a conversation with the AI assistant</div>
          <div style={{ fontSize: '12px', marginTop: '8px' }}>
            The AI can help you with terminal commands, coding, and general questions
          </div>
        </div>
      </MessagesContainer>
    );
  }

  return (
    <MessagesContainer>
      {messages.map((message) => {
        const isUser = message.role === 'user';
        
        return (
          <MessageItem key={message.id} $isUser={isUser}>
            <MessageAvatar 
              $isUser={isUser}
              icon={isUser ? <UserOutlined /> : <RobotOutlined />}
              size="small"
            />
            
            <div style={{ flex: 1, minWidth: 0 }}>
              <MessageBubble
                $isUser={isUser}
                $isStreaming={message.isStreaming}
              >
                {hasToolCalls(message) ? (
                  <div>
                    {/* Render mixed content if available */}
                    {message.mixedContent && message.mixedContent.length > 0 ? (
                      <MixedContentRenderer
                        mixedContent={message.mixedContent}
                        isUser={isUser}
                      />
                    ) : (
                      <>
                        {/* Render regular content */}
                        {message.content && (
                          <MessageContent
                            $isUser={isUser}
                            dangerouslySetInnerHTML={{
                              __html: renderMessageContent(message)
                            }}
                          />
                        )}
                        {/* Render tool calls separately if no mixed content */}
                        {message.tool_calls && message.tool_calls.map((toolCall, index) => (
                          <div key={`tool-${index}`} style={{ marginTop: '8px' }}>
                            <MixedContentRenderer
                              mixedContent={[{ type: 'tool_call', toolCall }]}
                              isUser={isUser}
                            />
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                ) : (
                  <MessageContent
                    $isUser={isUser}
                    dangerouslySetInnerHTML={{
                      __html: renderMessageContent(message)
                    }}
                  />
                )}
              </MessageBubble>
              
              <MessageMeta $isUser={isUser}>
                <MessageTime>
                  {formatTime(message.timestamp)}
                </MessageTime>
                {message.isStreaming && (
                  <Text type="secondary" style={{ fontSize: '11px' }}>
                    Typing...
                  </Text>
                )}
              </MessageMeta>
            </div>
          </MessageItem>
        );
      })}
      <div ref={messagesEndRef} />
    </MessagesContainer>
  );
};

export default MessageList;
