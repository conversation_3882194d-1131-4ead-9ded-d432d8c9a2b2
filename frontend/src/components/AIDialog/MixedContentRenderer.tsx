import React from 'react';
import { marked } from 'marked';
import styled from 'styled-components';
import ToolCallRenderer from './ToolCallRenderer';
import type { MixedStreamingMessage } from '@/types';

const MixedContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ContentBlock = styled.div`
  line-height: 1.6;
  
  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  pre {
    background: #f5f5f5;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    overflow-x: auto;
    font-size: 13px;
    line-height: 1.4;
  }
  
  code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 13px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
  
  pre code {
    background: none;
    padding: 0;
  }
  
  blockquote {
    border-left: 4px solid #1890ff;
    margin: 8px 0;
    padding: 8px 16px;
    background: #f0f9ff;
    color: #666;
  }
  
  ul, ol {
    margin: 8px 0;
    padding-left: 24px;
  }
  
  li {
    margin: 4px 0;
  }
  
  h1, h2, h3, h4, h5, h6 {
    margin: 16px 0 8px 0;
    color: #262626;
    
    &:first-child {
      margin-top: 0;
    }
  }
  
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 8px 0;
  }
  
  th, td {
    border: 1px solid #e8e8e8;
    padding: 8px 12px;
    text-align: left;
  }
  
  th {
    background: #fafafa;
    font-weight: 600;
  }
`;

interface MixedContentRendererProps {
  mixedContent: MixedStreamingMessage[];
  isUser?: boolean;
}

const MixedContentRenderer: React.FC<MixedContentRendererProps> = ({ 
  mixedContent, 
  isUser = false 
}) => {
  const renderContent = (content: string): string => {
    if (isUser) {
      // For user messages, just return plain text with line breaks
      return content.replace(/\n/g, '<br>');
    } else {
      // For assistant messages, render markdown
      try {
        const result = marked(content);
        return typeof result === 'string' ? result : content.replace(/\n/g, '<br>');
      } catch (error) {
        console.error('Failed to render markdown:', error);
        return content.replace(/\n/g, '<br>');
      }
    }
  };

  if (!mixedContent || mixedContent.length === 0) {
    return null;
  }

  return (
    <MixedContentContainer>
      {mixedContent.map((item, index) => {
        if (item.type === 'content' && item.content) {
          return (
            <ContentBlock
              key={`content-${index}`}
              dangerouslySetInnerHTML={{
                __html: renderContent(item.content)
              }}
            />
          );
        } else if (item.type === 'tool_call' && item.toolCall) {
          return (
            <ToolCallRenderer
              key={`tool-${index}`}
              toolCall={item.toolCall}
            />
          );
        }
        return null;
      })}
    </MixedContentContainer>
  );
};

export default MixedContentRenderer;
