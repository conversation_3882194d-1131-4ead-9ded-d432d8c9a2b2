// WebSocket related types
export interface WebSocketMessage {
  type: 'input' | 'output' | 'error' | 'status' | 'resize';
  data: string;
}

export interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

// Terminal related types
export interface TerminalConfig {
  fontFamily: string;
  fontSize: number;
  lineHeight: number;
  cursorBlink: boolean;
  cursorStyle: 'block' | 'underline' | 'bar';
  theme: TerminalTheme;
}

export interface TerminalTheme {
  background: string;
  foreground: string;
  cursor: string;
  selection: string;
}

export interface TerminalState {
  isReady: boolean;
  buffer: string[];
  config: TerminalConfig;
}

// Virtual Keyboard types
export interface KeyboardKey {
  key: string;
  label?: string;
  width?: number;
  shift?: boolean;
  ctrl?: boolean;
  alt?: boolean;
  meta?: boolean;
}

export interface KeyboardLayout {
  name: string;
  rows: KeyboardKey[][];
}

export interface KeyboardState {
  isVisible: boolean;
  currentLayout: string;
  shiftPressed: boolean;
  ctrlPressed: boolean;
  altPressed: boolean;
}

// AI Dialog types
export interface ToolCall {
  id: string;
  function: {
    name: string;
    arguments: string;
  };
  status?: 'pending' | 'running' | 'complete' | 'error';
  result?: any;
  error?: string;
}

export interface MixedStreamingMessage {
  type: 'content' | 'tool_call';
  content?: string;
  toolCall?: ToolCall;
}

export interface AIMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
  tool_calls?: ToolCall[];
  tool_call_id?: string;
  mixedContent?: MixedStreamingMessage[];
}

export interface AIDialogState {
  isOpen: boolean;
  messages: AIMessage[];
  isLoading: boolean;
  error: string | null;
}

// Session management types
export interface SessionInfo {
  remainingTime: number;
  isActive: boolean;
  canExtend: boolean;
}

export interface SessionState {
  info: SessionInfo;
  timer: number | null;
}

// UI State types
export interface UIState {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  loading: boolean;
}

// Service interfaces
export interface XTermService {
  initialize: (terminal: any, socket: WebSocket) => void;
  sendRawData: (data: string) => Promise<boolean>;
  sendKey: (key: string) => Promise<boolean>;
  sendCommand: (command: string, executeImmediately?: boolean) => Promise<boolean>;
  getAllContent: () => string;
  isServiceReady: () => boolean;
}

export interface WebSocketManager {
  connect: () => void;
  disconnect: () => void;
  send: (data: string) => boolean;
  getConnectionState: () => string;
  onOpen?: () => void;
  onMessage?: (event: MessageEvent) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
}

// Component Props types
export interface TerminalProps {
  className?: string;
  onReady?: () => void;
}

export interface VirtualKeyboardProps {
  visible?: boolean;
  onKeyPress?: (keyData: KeyboardKey) => void;
  onToggle?: () => void;
}

export interface AIDialogProps {
  open?: boolean;
  onClose?: () => void;
}

export interface StatusBarProps {
  sessionInfo: SessionInfo;
  onExtend: () => void;
}

// Store types
export interface Store<T> {
  subscribe: (listener: (state: T) => void) => () => void;
  getState: () => T;
  setState: (partial: Partial<T> | ((state: T) => Partial<T>)) => void;
}
